import { IConfiguration, Channel, AdType, BreakConnector, OrderType } from 'adpod-tools';

export const scenario4: IConfiguration = {
  id: '04',
  duration: 15,
  version: 'v1_0_0',
  channel: Channel.ttv,
  time: '2020-09-29T22:30:05+02:00',
  adslot: [
    {
      position: 1,
      duration: 15,
      type: AdType.tv,
      vastmirroredadsJson: {
        VAST: {
          _attributes: {
            version: '2.0',
            'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
            xmlns: 'http://www.iab.com/VAST'
          },
          Ad: [
            {
              _attributes: {
                id: 'Spot_1',
                sequence: 1
              },
              InLine: {
                AdSystem: {
                  _text: 'TVN'
                },
                AdTitle: {
                  _text: 'TVN Video Ad'
                },
                Creatives: {
                  Creative: [
                    {
                      Linear: {
                        Duration: {
                          _text: '00:00:15'
                        },
                        MediaFiles: {
                          MediaFile: [
                            {
                              _attributes: {
                                delivery: 'progressive',
                                height: 404,
                                type: 'video/mp4',
                                width: 720
                              },
                              _cdata:
                                'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4'
                            }
                          ],
                          Mezzanine: {
                            _attributes: {
                              delivery: 'progressive',
                              height: 720,
                              type: 'video/mp4',
                              width: 1280
                            },
                            _cdata:
                              'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218.mov'
                          }
                        },
                        TrackingEvents: {
                          Tracking: [
                            {
                              _attributes: {
                                event: 'start'
                              },
                              _cdata:
                                'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                            },
                            {
                              _attributes: {
                                event: 'firstQuartile'
                              },
                              _cdata:
                                'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid%3A437816241764401437%7C_vtm%3A_AONOVTMVALUE_%7Cp%3A0%7Cw%3A0%7Cz%3A0%7Co%3A0%7Ca%3A0%7Cpn%3A0%7Con%3A0%7Cattempt%3Annpooppvoiykiognkgngngru_1598259485%7C/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15'
                            },
                            {
                              _attributes: {
                                event: 'midpoint'
                              },
                              _cdata:
                                'http://dai-discoveryengage.tvn.pl/?ed=5EF46361CF664908%7C_cdata%3A833577_0%2C854305_0%2C854340_0%2C1077725_0%2C1296341_0%2C1296356_0%2C1441789_0%2C1899198_0%2C2429758_0%2C2429761_0%7C_vid'
                            }
                          ]
                        }
                      }
                    }
                  ]
                },
                Impression: [],
                Error: [],
                Extensions: {
                  Extension: []
                }
              }
            }
          ]
        }
      },
      adrequest: '',
      connector: BreakConnector.adoceanSlotSchedule,
      metadata: {
        breakId: '123',
        orderId: '',
        adId: '',
        orderType: OrderType.empty
      }
    }
  ]
};
