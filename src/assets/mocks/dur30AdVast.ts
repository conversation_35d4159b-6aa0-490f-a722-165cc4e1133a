import { AdMetadata, AdVast4Normalized, AdVast4EmbeddedAdMetadata } from 'adpod-tools';

export const dur30AdVast: AdVast4Normalized = {
  _attributes: {
    id: 'Spot_4',
    sequence: 1
  },
  InLine: {
    AdSystem: {
      _text: 'TVN',
      _attributes: { version: '4.0' }
    },
    AdTitle: {
      _text: 'TVN Video Ad'
    },
    Creatives: {
      Creative: [
        {
          Linear: {
            Duration: {
              _text: '00:00:30'
            },
            MediaFiles: {
              MediaFile: [
                {
                  _attributes: {
                    width: 720,
                    type: 'video/mp4',
                    height: 404,
                    delivery: 'progressive'
                  },
                  _cdata:
                    'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/9f62b8625f914a002496335037e9ad97/d43ca65a-7d4f-46e5-bfbb-3e08a5858f0a-www.mp4'
                }
              ]
            },
            VideoClicks: {
              ClickThrough: {
                _attributes: {
                  id: ''
                }
              }
            },
            TrackingEvents: {
              Tracking: [
                {
                  _attributes: {
                    event: 'start'
                  }
                },
                {
                  _attributes: {
                    event: 'firstQuartile'
                  }
                },
                {
                  _attributes: {
                    event: 'midpoint'
                  }
                },
                {
                  _attributes: {
                    event: 'thirdQuartile'
                  }
                },
                {
                  _attributes: {
                    event: 'complete'
                  }
                }
              ]
            }
          }
        },
        {
          CompanionAds: undefined
        }
      ]
    },
    Impression: [{ _attributes: { id: '' } }],
    Error: [],
    Extensions: {
      Extension: [
        {
          _attributes: { type: 'wbdapm' },
          SlotParameters: {
            _cdata:
              '{ "breakID": "7515464971096321", "breakType": "mirrored", "slotPosition": 2, "slotType": "mirrored" }'
          }
        }
      ]
    }
  }
};

export const dur30AdVastMetadata: AdMetadata = {
  spotId: 'Spot_4',
  campaignId: 'CA_37178,OR_5,CR_8',
  breakId: '7515464971096321',
  linear: 'true',
  conditionalAd: false
};

export const dur30AdWithMetadata: AdVast4EmbeddedAdMetadata = {
  adVast: dur30AdVast,
  adMetadata: dur30AdVastMetadata
};
