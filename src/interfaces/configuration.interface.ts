import { AdVast4Normalized, AdVast4EmbeddedAdMetadata, BreakConnector } from 'adpod-tools';
import { slotFWTrackingScriptsType } from './jsonVast.type';

export type AdServerVastType = AdVast4Normalized[] | null;

export type IAdVastJson4Attrs = {
  id: string;
  breakId: string;
  sequence: number;
  timeOffset?: string;
  campaignId?: string;
  conditionalAd?: boolean;
  linear?: string;
};

export type breakFWTrackingScripts = {
  slotImpressionUrl: string | undefined;
  slotEndUrl: string | undefined;
};

interface AdserverCommon {
  position: number | null;
  adServerUrl: string;
  connector: BreakConnector;
  breakId: string;
  isReplaced: boolean;
}

export interface AdserverAdPreParse extends AdserverCommon {
  vast: string | null;
}

export interface AdserverAd extends AdserverCommon {
  ads: AdVast4EmbeddedAdMetadata[];
  isProgrammatic?: boolean[];
}

export interface AdserverAdFreeWheel extends AdserverAd {
  daiAdsDuration: number | null;
  slotFWTrackingScripts?: slotFWTrackingScriptsType[];
  breakFWTrackingScripts?: breakFWTrackingScripts;
  mezzanineAdId: (string | null)[];
  universalAdId?: string[] | null;
}

export interface AdserverAdGAM extends AdserverAd {
  universalAdId?: string | null;
  isProgrammaticAd?: boolean;
}
