import { AdserverAd } from './configuration.interface';
import { Vast4Normalized, Vast4EmbeddedAdMetadata, VmapNormalized } from 'adpod-tools';

export type IVastJsonOperationalData = {
  vast4Json: Vast4EmbeddedAdMetadata;
  isWithReplacedAds: boolean;
  adServerResponseLog: AdserverAd[];
};

export enum TrackingScriptsEnum {
  tracking = 'tracking',
  impression = 'impression'
}

export type slotFWTrackingScriptsType = {
  adIndex: number;
  name: string;
  value: string;
  type: TrackingScriptsEnum | null;
};

export type TrackingType = {
  _attributes: { event: string };
  _cdata: string;
};

export type VastJsonPlaylistType = Vast4Normalized | VmapNormalized;
