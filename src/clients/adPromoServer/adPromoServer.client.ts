import {
  AdVast4EmbeddedAdMetadata,
  Channel,
  LogLevel,
  normalizeVast4,
  request,
  Vast4EmbeddedAdMetadata,
  Vast4Normalized,
  xmlParser
} from 'adpod-tools';
import logger from '../../libs/logging/logger';
import { URLParamsHelper } from '../../scripts/adserver/urlHelper';
import { RequestHeaders } from '../../interfaces';
import {
  AdPromoServerDurationBasedVmap,
  IAdPromoServerClient
} from './adPromoServerClient.interface';
import { validators } from '../../EnvValidation/envalidConfig';
import { VmapEmbeddedAdMetadataMonoAdBreak } from '../freewheel/freewheel.client';

export class AdPromoServerClient implements IAdPromoServerClient {
  async getFillerAds(
    channel: Channel,
    duration: number,
    headers: RequestHeaders
  ): Promise<{ response: VmapEmbeddedAdMetadataMonoAdBreak | null; url: string }> {
    const url = new URLParamsHelper(validators.AD_PROMO_SERVER_URL, '&')
      .add('dur', duration)
      .add('ch', channel)
      .toString();

    try {
      logger('SO_ADPROMO_REQUEST', { url, headers }, LogLevel.startOver);

      const ua = headers['x-device-user-agent'];
      const requestInit = ua
        ? { headers: { 'user-agent': ua, 'x-device-user-agent': ua } }
        : undefined;

      const response = await request(url, requestInit);

      const responseText = (await response.text()) as string;

      logger(
        'SO_ADPROMO_RESPONSE',
        {
          responseStatus: response.status,
          responseStatusText: response.statusText,
          responseHeaders: response.statusText,
          responseOk: response.ok,
          responseType: response.type,
          responseUrl: response.url,
          responseText
        },
        LogLevel.startOver
      );

      const jsonResult = xmlParser.fromXMLtoJSON(
        responseText
      ) as AdPromoServerDurationBasedVmap;

      logger('SO_ADPROMO_XML_TO_JSON', { xmlToJson: jsonResult }, LogLevel.startOver);

      return { response: this.normalizeAdPromoServerDurationBasedVmap(jsonResult), url };
    } catch (err) {
      logger('ERROR_SO_ADPROMO_FETCH', { err }, LogLevel.error);
      return { response: null, url };
    }
  }

  private normalizeAdPromoServerDurationBasedVmap(
    obj: AdPromoServerDurationBasedVmap
  ): VmapEmbeddedAdMetadataMonoAdBreak {
    const adData = obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'];

    obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'] =
      this.addVast4Metadata(normalizeVast4(adData)) as any;

    return obj as any as VmapEmbeddedAdMetadataMonoAdBreak;
  }

  private addVast4Metadata(vast4: Vast4Normalized): Vast4EmbeddedAdMetadata {
    const ads: AdVast4EmbeddedAdMetadata[] = vast4.VAST.Ad.map((adVast) => ({
      adVast,
      adMetadata: {
        spotId: adVast._attributes.id
      }
    }));

    return {
      VAST: {
        _attributes: vast4.VAST._attributes,
        Ad: ads
      }
    };
  }
}
