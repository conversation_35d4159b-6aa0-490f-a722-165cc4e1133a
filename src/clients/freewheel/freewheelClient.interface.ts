import {
  Channel,
  Overwrite,
  VMAP,
  Vmap,
  VmapAdBreak,
  VmapAdBreakNormalized
} from 'adpod-tools';
import { RequestHeaders } from '../../interfaces';
import { DeapItemType } from '../../scripts/services/deapProfiles.service';
import { VmapEmbeddedAdMetadataMonoAdBreak } from './freewheel.client';

export abstract class IFreewheelClient {
  abstract getDurationBasedAds(
    version: string,
    channel: Channel,
    headers: RequestHeaders,
    duration: number,
    deapProfiles: DeapItemType,
    uid?: string
  ): Promise<{ response: VmapEmbeddedAdMetadataMonoAdBreak | null; url: string }>;
}

export type FWDurationBasedVmap = Overwrite<Vmap, { 'vmap:VMAP': FWDurationBasedVMAP }>;

export type FWDurationBasedVMAP = Overwrite<VMAP, { 'vmap:AdBreak': VmapAdBreak }>;

// add metadata
export type FWDurationBasedVmapNormalized = Overwrite<
  Vmap,
  { 'vmap:VMAP': FWDurationBasedVMAPNormalized }
>;

export type FWDurationBasedVMAPNormalized = Overwrite<
  VMAP,
  { 'vmap:AdBreak': VmapAdBreakNormalized }
>;
