import { Injectable } from '@nestjs/common';
import { AdVast4EmbeddedAdMetadata, logger, LogLevel } from 'adpod-tools';
import { ConfigWithPlaylist } from '../../../interfaces/configWithPlaylist';

export abstract class IPlaylistMerger {}

@Injectable()
export class PlaylistMerger implements IPlaylistMerger {
  public merge(playlists: ConfigWithPlaylist[]): ConfigWithPlaylist {
    const startingIndex = 0;
    const shouldNotMergeWithOneVersion = playlists.length === 1;
    if (shouldNotMergeWithOneVersion) {
      return playlists[startingIndex];
    }

    const firstVast = playlists[startingIndex].breakDetails.vast4Json.VAST;

    const adslotsAfterMerge = firstVast.Ad.map((adslot, index) =>
      this.replaceAdslot(adslot, index, playlists, startingIndex, playlists[startingIndex])
    );

    firstVast.Ad = adslotsAfterMerge;

    return playlists[startingIndex];
  }

  public mergeMultiple(configsWithPlaylists: ConfigWithPlaylist[][]): ConfigWithPlaylist[] {
    const shouldNotMergeWithOneVersion = configsWithPlaylists.length === 1;
    if (shouldNotMergeWithOneVersion) {
      return configsWithPlaylists[0];
    }

    const filteredConfigsDetails = configsWithPlaylists.filter((x) => x.length > 0);
    const reorderedConfigsWithPlaylists = this.reorderConfigs(filteredConfigsDetails);

    return reorderedConfigsWithPlaylists.map((p) => this.merge(p));
  }

  private reorderConfigs(configs: ConfigWithPlaylist[][]) {
    logger('MERGER_REORDER_CONFIGS', undefined, LogLevel.dev);

    const result = configs.reduce<Map<number, ConfigWithPlaylist[]>>((acc, config) => {
      config.forEach((configDetails, index) => {
        acc.set(index, (acc.get(index) || []).concat(configDetails));
      });
      return acc;
    }, new Map());

    return Array.from(result.values());
  }

  private takeNextPlaylist(configIndex: number, configsWithPlaylists: ConfigWithPlaylist[]) {
    const nextConfigIndex = configIndex + 1;
    const nextConfigWithPlaylist = configsWithPlaylists[nextConfigIndex];

    return { nextConfigWithPlaylist, nextConfigIndex };
  }

  private setVersionInDebug(
    currentAdslot: AdVast4EmbeddedAdMetadata,
    currentConfigWithPlaylist: ConfigWithPlaylist
  ) {
    if (!currentAdslot.adVast.InLine?.Debug) {
      return;
    }

    currentAdslot.adMetadata.version = currentConfigWithPlaylist.config.version;
  }

  private replaceAdslot(
    currentAdslot: AdVast4EmbeddedAdMetadata,
    currentAdslotIndex: number,
    configsWithPlaylists: ConfigWithPlaylist[],
    configIndex: number,
    currentConfigWithPlaylist: ConfigWithPlaylist
  ): AdVast4EmbeddedAdMetadata {
    const metadata = currentAdslot.adVast.InLine?.Debug?.Metadata?._text;
    const metadataJson = metadata && JSON.parse(metadata);

    if (metadataJson?.isReplaced) {
      this.setVersionInDebug(currentAdslot, currentConfigWithPlaylist);

      return currentAdslot;
    }

    const { nextConfigIndex, nextConfigWithPlaylist } = this.takeNextPlaylist(
      configIndex,
      configsWithPlaylists
    );

    if (!nextConfigWithPlaylist) {
      const firstVast = configsWithPlaylists[0].breakDetails.vast4Json.VAST;
      return firstVast.Ad[currentAdslotIndex];
    }

    const nextVast = nextConfigWithPlaylist.breakDetails.vast4Json.VAST.Ad[currentAdslotIndex];

    return this.replaceAdslot(
      nextVast,
      currentAdslotIndex,
      configsWithPlaylists,
      nextConfigIndex,
      nextConfigWithPlaylist
    );
  }
}
