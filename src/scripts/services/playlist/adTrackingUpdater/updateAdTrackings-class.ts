import {
  IAd,
  Channel,
  AdTracking,
  getMarketByChannel,
  AdImpression,
  availableItalyChannels,
  hmsToMilliseconds,
  AdVast4EmbeddedAdMetadata
} from 'adpod-tools';
import { Protocol } from '../../../../models/protocol.model';
import { URLParamsHelper } from '../../../adserver/urlHelper';

type booleanAsString = 'true' | 'false';

export class AdTrackingsUpdater {
  private market: string;

  private _custParams: string = '';

  private tstCParam: booleanAsString | undefined;

  private _campaignAttr: string | undefined;

  private _freeWheelAdDuration: number | undefined;

  private _adIndex: number | undefined;

  constructor(
    private Ad: AdVast4EmbeddedAdMetadata,
    adIndex: number,
    private adData: IAd,
    private channel: Channel | availableItalyChannels,
    private version: string,
    private protocol: Protocol,
    private uid?: string,
    private isADO?: boolean,
    custParams?: string,
    private isWithMultipleAds?: boolean
  ) {
    this._adIndex = ++adIndex;
    this.custParams = custParams;
    this.market = getMarketByChannel(this.channel);
    this.setTstCParam();
    this.setCampaignAttr();
    this.setFreeWheelAdDuration();

    this.addAdditionalAdTrackings();
  }

  setTstCParam(): void {
    this.tstCParam = this.isADO
      ? this.Ad.adMetadata.test_campaign === 'true'
        ? 'true'
        : 'false'
      : undefined;
  }

  setCampaignAttr(): void {
    this._campaignAttr = this.isADO
      ? this.Ad.adMetadata.campaignId
      : (this.Ad.adVast.InLine?.AdTitle?._text ?? '');
  }

  setFreeWheelAdDuration(): void {
    const adDuration = this.Ad.adVast.InLine?.Creatives.Creative[0].Linear?.Duration._text;

    if (!(adDuration && this.isWithMultipleAds)) {
      return;
    }

    const durationInMs = hmsToMilliseconds(adDuration);

    if (!durationInMs) {
      return;
    }

    this._freeWheelAdDuration = durationInMs / 1000;
  }

  get campaignAttr(): string | undefined {
    return this._campaignAttr;
  }

  addAdditionalAdTrackings(): void {
    if (this.Ad.adVast.InLine) {
      this.addAdditionalImpression();
    }

    if (!this.haveAdTrackings()) {
      return;
    }

    const creativeValue = this.Ad.adVast.InLine?.Creatives.Creative;

    if (!creativeValue) {
      return;
    }

    creativeValue.forEach((creative, index) => {
      if (!creative.Linear) {
        return;
      }

      const currentTrackings = creative.Linear.TrackingEvents.Tracking;

      const universalAdIdValue = creative.UniversalAdId?._text ?? 'not provided';

      this.Ad.adVast.InLine!.Creatives.Creative[index].Linear!.TrackingEvents.Tracking =
        this.getAdditionalAdTrackings(currentTrackings, universalAdIdValue);
    });
  }

  haveAdTrackings(): boolean {
    const creativeValue = this.Ad.adVast.InLine?.Creatives.Creative;

    return !!creativeValue?.[0].Linear?.TrackingEvents.Tracking;
  }

  getAdditionalAdTrackings(
    currentTrackings: AdTracking[],
    universalAdId?: string
  ): AdTracking[] {
    const {
      position,
      duration,
      metadata: { breakId, adId }
    } = this.adData;

    const createEventFullUrl = (e: string) => {
      const helper = new URLParamsHelper('', '%2F');

      helper
        .add('p', position)
        .add('bid', breakId)
        .add('ch', this.channel)
        .addMaybe('uadid', adId)
        .add('v', this.version)
        .addMaybe('uid', this.uid)
        .add('e', e)
        .add('mode', 'dai')
        .addMaybe('dai_ad', universalAdId)
        .addMaybe('tst_c', this.tstCParam)
        .add('dur', `${duration}`)
        .addMaybe('c', this.campaignAttr)
        .add('bt', 'dai')
        .addMaybeRaw('cust_params', decodeURIComponent(this.custParams))
        .add('m', this.market)
        .add('t', 'dai')
        .addMaybe('m_slot', this.isWithMultipleAds && 'true')
        .addMaybe('m_slot_dur', this._freeWheelAdDuration)
        .addMaybe('m_slot_p', this.isWithMultipleAds && this._adIndex)
        .addMaybe('app_version', process.env.npm_package_version);

      return `${this.protocol}://dai-discoveryengage.tvn.pl/?ed=${helper.toString()}`;
    };

    const additionalTrackings = [
      { _attributes: { event: 'start' }, _cdata: createEventFullUrl('0') },
      { _attributes: { event: 'firstQuartile' }, _cdata: createEventFullUrl('25') },
      { _attributes: { event: 'midpoint' }, _cdata: createEventFullUrl('50') },
      { _attributes: { event: 'thirdQuartile' }, _cdata: createEventFullUrl('75') },
      { _attributes: { event: 'complete' }, _cdata: createEventFullUrl('100') }
    ];

    const currentWithAdditional = [...currentTrackings, ...additionalTrackings];

    return currentWithAdditional;
  }

  getAdditionalAdImpressions(): AdImpression[] {
    const {
      position,
      duration,
      metadata: { breakId, adId }
    } = this.adData;

    const creatives = this.Ad.adVast.InLine?.Creatives.Creative;

    // add additional impression scripts for all cases (each adServer, trackingDaiAds enabled)
    const createEventFullUrl = (e: string) => {
      const helper = new URLParamsHelper('', '%2F');

      const dai_ad: string = !!creatives
        ? creatives
            .filter((creative) => !!creative.Linear?.Duration)
            .map((creative) => creative.UniversalAdId?._text ?? 'not provided')
            .join(',')
        : 'not provided';

      helper
        .add('p', position)
        .add('bid', breakId)
        .add('ch', this.channel)
        .addMaybe('uadid', adId)
        .add('v', this.version)
        .addMaybe('uid', this.uid)
        .add('e', e)
        .add('mode', 'dai')
        .addMaybe('dai_ad', dai_ad)
        .addMaybe('tst_c', this.tstCParam)
        .add('dur', duration)
        .addMaybe('c', this.campaignAttr)
        .add('bt', 'dai')
        .addMaybeRaw('cust_params', decodeURIComponent(this.custParams))
        .add('m', this.market)
        .add('t', 'dai')
        .addMaybe('m_slot', this.isWithMultipleAds && 'true')
        .addMaybe('m_slot_dur', this._freeWheelAdDuration)
        .addMaybe('m_slot_p', this.isWithMultipleAds && this._adIndex)
        .addMaybe('app_version', process.env.npm_package_version);

      return `${this.protocol}://dai-discoveryengage.tvn.pl/?ed=${helper.toString()}`;
    };

    const additionalImpression: AdImpression[] = [
      {
        _attributes: { id: '' },
        _cdata: createEventFullUrl('impression')
      }
    ];

    return additionalImpression;
  }

  addAdditionalImpression(): AdVast4EmbeddedAdMetadata {
    if (this.Ad.adVast.InLine?.Impression) {
      const additionalImpression = this.getAdditionalAdImpressions();
      this.Ad.adVast.InLine.Impression.push(...additionalImpression);
    }

    return this.Ad;
  }

  set custParams(params: string | undefined) {
    if (!params) {
      this._custParams = '';
      return;
    }
    const custParamsWithoutGdpr = new URLParamsHelper(params)
      .delete('gdpr_consent')
      .delete('gdpr')
      .toString();

    this._custParams = encodeURIComponent(custParamsWithoutGdpr);
  }

  get custParams(): string {
    return this._custParams;
  }

  getUpdatedAd(): AdVast4EmbeddedAdMetadata {
    return this.Ad;
  }
}
