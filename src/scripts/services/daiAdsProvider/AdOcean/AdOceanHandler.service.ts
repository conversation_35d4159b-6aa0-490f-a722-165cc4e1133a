import { Injectable } from '@nestjs/common';
import { LogLevel, IAd, logger, AdVast4EmbeddedAdMetadata } from 'adpod-tools';
import { AdOceanBreakDaiAdsProvider } from './AdOceanBreakDaiAdsProvider';
import { AdOceanProxyDaiAdsProvider } from './AdOceanProxyDaiAdsProvider';
import { DaiAdsProviderFactory } from '../daiAdsProviderFactory';
import { UltimateDaiAdsProviderInput, AdserverAd } from '../../../../interfaces';

@Injectable()
export class AdOceanHandler {
  constructor(
    private readonly adsFactory: DaiAdsProviderFactory,
    private readonly ADOProxyProvider: AdOceanProxyDaiAdsProvider,
    private readonly ADOBreakProvider: AdOceanBreakDaiAdsProvider
  ) {}

  public async provideADOAds(inputArgs: UltimateDaiAdsProviderInput): Promise<AdserverAd[]> {
    const {
      configuration: { breakAdRequest, breakConnector, adslot }
    } = inputArgs;

    const isWithBreakRequest: boolean = !!(breakAdRequest && breakConnector);
    const isWithAdSlotsRequests: boolean = adslot.some((s) => !!s.adrequest);

    if (isWithBreakRequest && isWithAdSlotsRequests) {
      return this.getCombinedAds(inputArgs);
    }
    if (isWithBreakRequest) {
      return this.getBreakAds(inputArgs);
    }
    return this.getProxyAds(inputArgs);
  }

  private async getBreakAds(inputArgs: UltimateDaiAdsProviderInput): Promise<AdserverAd[]> {
    const initData = await this.adsFactory.initBreak(inputArgs);
    const breakResponse = await this.ADOBreakProvider.provideAdCoordinator(initData);

    logger(
      'BREAK_RESPONSE',
      {
        breakResponse: breakResponse
          .filter((sr) => !!this.getAdIdFromVast(sr.ads))
          .map((br) => br.position)
      },
      LogLevel.dev
    );

    return breakResponse;
  }

  private async getProxyAds(inputArgs: UltimateDaiAdsProviderInput): Promise<AdserverAd[]> {
    const initData = await this.adsFactory.init(inputArgs);
    const slotsResponse = await this.ADOProxyProvider.provideAdCoordinator(initData);

    logger(
      'SLOTS_RESPONSE',
      {
        slotsResponse: slotsResponse
          .filter((sr) => !!this.getAdIdFromVast(sr.ads))
          .map((sr) => sr.position)
      },
      LogLevel.dev
    );

    return slotsResponse;
  }

  private async getCombinedAds(inputArgs: UltimateDaiAdsProviderInput): Promise<AdserverAd[]> {
    const breakResponse = await this.getBreakAds(inputArgs);
    const slotsResponse = await this.getProxyAds(inputArgs);

    const replacedAdsResponse: AdserverAd[] = [];

    inputArgs.configuration.adslot.forEach((slot) => {
      const slotAd = slotsResponse.find((s) => this.checkPosition(slot, s));
      const breakAd = breakResponse.find((s) => this.checkPosition(slot, s));

      if (!!slotAd) {
        replacedAdsResponse.push(slotAd);
      } else if (!!breakAd) {
        replacedAdsResponse.push(breakAd);
      }
    });

    logger(
      'COMBINED_RESPONSE',
      {
        replacedAdsResponse: replacedAdsResponse.map((ad) => ({
          position: ad.position,
          connector: ad.connector
        }))
      },
      LogLevel.dev
    );

    return replacedAdsResponse;
  }

  private checkPosition(configSlot: IAd, replacedSlot: AdserverAd): boolean {
    return (
      configSlot.position === replacedSlot.position && !!this.getAdIdFromVast(replacedSlot.ads)
    );
  }

  private getAdIdFromVast(ads: AdVast4EmbeddedAdMetadata[] | null): string | undefined {
    if (!ads?.length) {
      return undefined;
    }

    const creative = ads[0].adVast.InLine?.Creatives.Creative;

    if (!creative || creative.length === 0) {
      return undefined;
    }

    return creative[0]._attributes?.id;
  }
}
