import {
  AdVast4EmbeddedAdMetadata,
  xmlParser,
  LogLevel,
  Vast4,
  normalizeVast4,
  BreakConnector,
  logger
} from 'adpod-tools';
import {
  AdserverAd,
  AdserverAdPreParse,
  DaiAdsProviderAtvSpotsType,
  DaiAdsProviderOutput,
  DaiAdsRequestCommon,
  logRequestStatsInputATV,
  fetchAdsOutput
} from '../../../../interfaces';
import { Injectable } from '@nestjs/common';
import { validators } from '../../../../EnvValidation/envalidConfig';
import axios from 'axios';
import { IDebugService } from '../../../../libs/caching/services/debug.service';
import { isEmptyVast } from '../../../vast/isEmptyVast';
import { createADORequestUrl } from '../../../adserver/createADORequestUrl';
import {
  getCurrentPerformanceTime,
  formatPerformanceTime
} from '../../../utils/performanceTime';
import { DaiAdsProvider_REST } from '../abstractDaiAdsProvider';

@Injectable()
export class AdOceanProxyDaiAdsProvider extends DaiAdsProvider_REST {
  constructor(private readonly debugService: IDebugService) {
    super();
  }

  public async provideAdCoordinator(inputArgs: DaiAdsProviderOutput): Promise<AdserverAd[]> {
    const { daiAds, startFetchTime, endFetchTime } = await this.fetchAds(inputArgs);

    const daiAdsExtractedData = await this.extractDataFromDaiAds(daiAds);

    const { atvSpots, version, channel, connector } = inputArgs.config;

    const filledSlots = daiAdsExtractedData.filter(({ ads }) => !!ads.length).length;

    this.logAdServerRequestStats({
      startFetchTime,
      endFetchTime,
      requestedSlots: atvSpots.length,
      returnedSlots: daiAdsExtractedData.length,
      filledSlots,
      version,
      connector,
      channel,
      isProgrammatic: daiAdsExtractedData.some(({ isProgrammatic }) => isProgrammatic)
    });

    if (filledSlots === 0) {
      return daiAdsExtractedData;
    }

    return this.handleExtractedData(atvSpots, daiAdsExtractedData);
  }

  private async handleExtractedData(
    atvSpots: DaiAdsProviderAtvSpotsType[],
    daiAds: AdserverAd[]
  ): Promise<AdserverAd[]> {
    const noProgrammaticHandler = this.fillNoProgrammaticClosure(atvSpots);

    const resolvedDaiAds = await Promise.all(
      daiAds.map(async (el) => noProgrammaticHandler(el))
    );

    return resolvedDaiAds.filter((el) => !!el) as AdserverAd[];
  }

  protected async fetchAds(inputArgs: DaiAdsProviderOutput): Promise<fetchAdsOutput> {
    const startFetchTime = getCurrentPerformanceTime();

    const allResponses: AdserverAdPreParse[] = await Promise.all(
      inputArgs.config.atvSpots.map((spot) =>
        this.requestAd(
          spot,
          inputArgs.request,
          inputArgs.config.breakId,
          inputArgs.config.version,
          inputArgs.config.connector
        )
      )
    ).catch((error) => {
      logger('ERROR_HANDLING_ADSERVER_VASTS', { error }, LogLevel.error);
      return [];
    });

    const endFetchTime = getCurrentPerformanceTime();

    const daiAds = allResponses.filter(({ vast }) => !!vast);

    return { startFetchTime, endFetchTime, daiAds };
  }

  protected logAdServerRequestStats(args: logRequestStatsInputATV): void {
    const {
      startFetchTime,
      endFetchTime,
      requestedSlots,
      returnedSlots,
      filledSlots,
      version,
      connector,
      channel,
      isProgrammatic
    } = args;
    const processingTime = +formatPerformanceTime(startFetchTime, endFetchTime);

    const isWarn =
      processingTime > validators.LOG_ADSERVER_RESPONSE_PROCESSING_TIME_WARN_THRESHOLD;

    logger(
      `${isWarn ? 'WARN_' : ''}STATS_ADOCEAN_SLOT_SCHEDULE`,
      {
        requestedSlots,
        returnedSlots,
        filledSlots,
        processingTime,
        version,
        connector,
        channel,
        isProgrammatic
      },
      isWarn ? LogLevel.warn : LogLevel.statsAdserverAdo
    );
  }

  async requestAd(
    spot: DaiAdsProviderAtvSpotsType,
    providerRequest: DaiAdsRequestCommon,
    breakId: string,
    version: string,
    connector: BreakConnector
  ): Promise<AdserverAdPreParse> {
    const { adrequest, position } = spot;

    const { gdpr, gdprConsent, npa } = providerRequest;

    const adServerUrl = this.handleGdprsForAdServerUrl(
      createADORequestUrl(adrequest, providerRequest),
      { value: gdpr, name: 'gdpr' },
      { value: gdprConsent, name: 'gdpr_consent' },
      npa,
      '/'
    );

    const response: AdserverAdPreParse = {
      vast: null,
      position,
      adServerUrl,
      breakId,
      connector: BreakConnector.adoceanSlotSchedule,
      isReplaced: false
    };

    const timeoutMsg = 'AdOcean request timeout';
    const requestHeaders = {
      'user-agent': providerRequest.ua,
      'x-device-user-agent': providerRequest.ua,
      'x-device-ip': providerRequest.ip,
      'x-forwarded-for': providerRequest.ip,
      'x-device-referrer': providerRequest.headers['x-device-referrer']
    };
    const timeout = validators.AD_OCEAN_REQUEST_TIMEOUT_MS;

    const baseRequestDetails = {
      adServerUrl,
      requestHeaders,
      timeout,
      breakId
    };

    logger(
      'ADOCEAN_SLOT_SCHEDULE_REQUEST_DETAILS',
      { ...baseRequestDetails },
      LogLevel.adoSlotDebug
    );

    const performanceStart = performance.now();

    try {
      const resData = await axios.get(adServerUrl, {
        headers: requestHeaders,
        timeout,
        timeoutErrorMessage: timeoutMsg,
        responseType: 'text'
      });

      const performanceDone = performance.now();

      const { data, status, statusText, headers: responseHeaders } = resData;
      const responseLength = data?.length;

      logger(
        'ADOCEAN_SLOT_SCHEDULE_RESPONSE_DETAILS',
        {
          performanceMs: +(performanceDone - performanceStart).toFixed(0),
          statusText,
          status,
          responseLength,
          responseContent: responseLength < 3000 ? data : 'n/a',
          ...baseRequestDetails,
          responseHeaders
        },
        LogLevel.adoSlotDebug
      );

      response.vast = data as string;
      response.isReplaced = !isEmptyVast(data);
    } catch (err: unknown) {
      const performanceFail = performance.now();
      const performanceMs = +(performanceFail - performanceStart).toFixed(0);

      if (err instanceof Error && err.message === timeoutMsg) {
        logger(
          'WARN_ADOCEAN_SLOT_SCHEDULE_AXIOS_TIMEOUT',
          {
            performanceMs,
            errorData: err,
            ...baseRequestDetails
          },
          LogLevel.warn
        );
      } else {
        logger(
          'ERROR_ADOCEAN_SLOT_SCHEDULE_REQUEST',
          {
            performanceMs,
            errorData: err,
            ...baseRequestDetails
          },
          LogLevel.error
        );
      }
    }

    await this.debugService.setSpotDebugDetails({
      breakId,
      adServerUrl,
      version,
      position,
      connector
    });

    return response;
  }

  protected async parseResponse(
    jsonVast: object | null
  ): Promise<AdVast4EmbeddedAdMetadata[]> {
    if (!jsonVast) {
      return [];
    }

    return this.handleAdDataTag(jsonVast as Vast4);
  }

  protected async extractDataFromDaiAds(daiAds: AdserverAdPreParse[]): Promise<AdserverAd[]> {
    const daiAdsExtractedData = await Promise.all(
      daiAds.map(async ({ vast, ...res }: AdserverAdPreParse): Promise<AdserverAd> => {
        const jsonVast = xmlParser.fromXMLtoJSON(vast) as Vast4 | null;

        const ads = await this.parseResponse(jsonVast);

        return { ...res, ads };
      })
    );

    return daiAdsExtractedData;
  }

  private handleAdDataTag(jsonVast: Vast4 | null): AdVast4EmbeddedAdMetadata[] {
    if (!jsonVast) {
      return [];
    }

    const vast4Normalized = normalizeVast4(jsonVast);
    return this.addAdVast4Metadata(vast4Normalized);
  }

  private fillNoProgrammaticClosure(
    atvSpots: DaiAdsProviderAtvSpotsType[]
  ): (res: AdserverAd) => AdserverAd | null {
    const linearConfigAds = atvSpots
      .map(({ adId }) => adId)
      .filter((spot) => !!spot) as string[];

    const getNonlinearConfigAd = (res: AdserverAd): boolean => {
      if (res.ads.length === 0) {
        return true;
      }

      const creative = res.ads[0].adVast.InLine?.Creatives.Creative;

      if (!creative || creative.length === 0) {
        return true;
      }

      const adId = creative[0]._attributes?.id;

      return !adId || !linearConfigAds.includes(adId);
    };

    return (res: AdserverAd): AdserverAd | null => {
      return getNonlinearConfigAd(res) ? res : null;
    };
  }
}
