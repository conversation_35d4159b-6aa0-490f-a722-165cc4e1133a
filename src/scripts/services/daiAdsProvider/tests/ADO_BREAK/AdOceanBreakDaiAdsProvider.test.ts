import { AdOceanBreakDaiAdsProvider } from '../../AdOcean/AdOceanBreakDaiAdsProvider';
import { outputData } from './outputFromFactory';
import { DaiAdsProviderFactory } from '../../daiAdsProviderFactory';
import { Test, TestingModule } from '@nestjs/testing';
import { CustomParamsGenerator } from '../../../customParamsGenerator.service';
import { RequestMacroParams } from '../../../../configuration/injectReqParams/requestMacroParams';
import { Protocol } from '../../../../../models/protocol.model';
import { response1, response2 } from './initialResponseBreak';
import { scenario5 } from '../../../../../assets/mocks/TEST/v1_0_0/scenario5';
import { BreakConnector } from 'adpod-tools';
import { IDeapProfilesService, DeapProfileService } from '../../../deapProfiles.service';
import axios from 'axios';
import outputDataADOBreak from './mockValidOutput';
import { TcfService } from '../../../tcf.service';
import {
  FreeWheelFillersAdsProviderService,
  IFreeWheelFillersAdsProviderService
} from '../../FreeWheel/freeWheelFillersAdsProvider.service';
import { FillerAdsService, IFillerAdsService } from 'adpod-aws';
import { TestRedisModule } from 'adpod-aws/dist/testing';
import { IDebugService, DebugService } from '../../../../../libs';
import { TestCacheModule } from '../../../../../libs/testing';

jest.mock('adpod-tools/dist/common/logging/logger');

describe('AdOcean Break service test suite', () => {
  let service: AdOceanBreakDaiAdsProvider;
  let factory: DaiAdsProviderFactory;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestRedisModule],
      providers: [
        AdOceanBreakDaiAdsProvider,
        {
          provide: IDebugService,
          useClass: DebugService
        }
      ]
    }).compile();

    service = app.get(AdOceanBreakDaiAdsProvider);

    const factoryApp: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestRedisModule],
      providers: [
        DaiAdsProviderFactory,
        CustomParamsGenerator,
        TcfService,
        {
          provide: IFreeWheelFillersAdsProviderService,
          useClass: FreeWheelFillersAdsProviderService
        },
        {
          provide: IFillerAdsService,
          useClass: FillerAdsService
        },
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        },
        {
          provide: IDebugService,
          useClass: DebugService
        }
      ]
    }).compile();

    factory = factoryApp.get(DaiAdsProviderFactory);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  test('should call adserverConnectorBreak - after refactor', async () => {
    const requestSpy = jest.spyOn(axios, 'get');
    requestSpy.mockResolvedValue({ data: await response1.text() });

    const result = await service.provideAdCoordinator(outputData);

    expect(result).toEqual(outputDataADOBreak);
  });

  test('should call adserverConnectorBreak - before refactor', async () => {
    const requestSpy = jest.spyOn(axios, 'get');
    requestSpy.mockResolvedValue({ data: await response2.text() });

    const requestMacroParams = new RequestMacroParams('12345', Protocol.https, '1');
    const inputArgs = await factory.initBreak({
      configuration: scenario5,
      headers: {},
      connector: BreakConnector.adoceanBreakSchedule,
      requestMacroParams
    });

    const result = await service.provideAdCoordinator(inputArgs);
    expect(requestSpy).toHaveBeenCalledTimes(1);
    expect(result).toMatchSnapshot();
  });
});
