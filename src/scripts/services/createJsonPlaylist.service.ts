/* eslint-disable no-loop-func */
import { Injectable } from '@nestjs/common';
import {
  IConfiguration,
  Channel,
  AdType,
  BreakConnector,
  availableItalyChannels,
  LogLevel,
  AdVast4EmbeddedAdMetadata,
  prepareJsonVast4WithMetadata
} from 'adpod-tools';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { RequestMacroParams } from '../configuration/injectReqParams/requestMacroParams';
import { findAppropriateAd } from '../ads/findAppropriateAd';
import {
  AdserverAd,
  RequestHeaders,
  IVastJsonOperationalData,
  UltimateDaiAdsProviderInput,
  AdserverAdFreeWheel
} from '../../interfaces';
import { getMirroredAd } from '../ads/getMirroredAd';
import { PlaylistMode } from '../../models/playlistMode.model';
import logger from '../../libs/logging/logger';
import { UltimateDaiAdsProvider } from './daiAdsProvider/UltimateDaiAdsProvider.service';
import { IFreeWheelFillersAdsProviderService } from './daiAdsProvider/FreeWheel/freeWheelFillersAdsProvider.service';
import { GeneralPlaylistTransformer } from './playlist/generalPlaylistTransformer.service';
import { IAdSlotPlaylistTransformer } from './playlist/adSlotPlaylistTransformer.service';
import { AdTrackingsUpdater } from './playlist/adTrackingUpdater/updateAdTrackings-class';

@Injectable()
export class JsonPlaylistService {
  constructor(
    private readonly daiAdsProvider: UltimateDaiAdsProvider,
    private readonly freeWheelFillersAdsProviderService: IFreeWheelFillersAdsProviderService,
    private readonly generalPlaylistTransformer: GeneralPlaylistTransformer,
    private readonly adSlotPlaylistTransformer: IAdSlotPlaylistTransformer
  ) {}

  public async create(
    configuration: IConfiguration,
    mode: PlaylistMode,
    channel: Channel | availableItalyChannels,
    version: string,
    requestMacroParams: RequestMacroParams,
    custParams?: string,
    output?: PlaylistOutputs,
    headers: RequestHeaders = {},
    ip?: string,
    ua?: string | string[]
  ): Promise<IVastJsonOperationalData> {
    const {
      id: breakId,
      time: breakTime,
      breakConnector,
      adslot: linearAds,
      metadata: breakMetadata
    } = configuration;

    const connector =
      linearAds.find((el) => el.type === AdType.atv)?.connector || BreakConnector.none;

    const isAdOceanConnector =
      breakConnector === BreakConnector.adoceanBreakSchedule ||
      connector === BreakConnector.adoceanSlotSchedule;

    const isGamConnector = breakConnector === BreakConnector.gamSchedule;

    const isMirroredMode = mode === PlaylistMode.mirrored;
    const isDebugMode = mode === PlaylistMode.debug;

    let adserverAds: AdserverAd[] = [];
    const playlistJson: AdVast4EmbeddedAdMetadata[] = [];

    // get adserver ads for non mirrored mode
    if (!isMirroredMode) {
      const providerConnector = (connector || breakConnector) ?? BreakConnector.none;

      if (this.daiAdsProvider.isImplementedProvider(providerConnector)) {
        const initData: UltimateDaiAdsProviderInput = {
          configuration,
          headers,
          connector,
          requestMacroParams,
          custParams,
          providerConnector,
          ip,
          ua
        };

        adserverAds = await this.daiAdsProvider.getDaiAds(initData);
      }
    }

    logger('DAI_ADS_PROVEDER_DATA', { adserverAds }, LogLevel.external); // temp external group

    let isWithReplacedAds = false;

    for (const linearAd of linearAds) {
      const {
        position: linearAdPosition,
        duration: linearAdDuration,
        type,
        timeOffset,
        metadata: { adId }
      } = linearAd;

      const isLinearSlot = type === AdType.tv;

      const timeOffsetWithFallback = timeOffset || breakTime;

      const adTimeOffset =
        output === PlaylistOutputs.vast4 ? timeOffsetWithFallback : undefined;

      // insert mirrored/liner ad for non replaceable slot
      if (isLinearSlot) {
        const mirroredAd: AdVast4EmbeddedAdMetadata | null = getMirroredAd(
          breakId,
          linearAd,
          adTimeOffset,
          isDebugMode
        );

        if (mirroredAd) playlistJson.push(mirroredAd);
        continue;
      }

      // match ad server ads (all Ad Servers) and fillers ads (FreeWheel only)
      const adServerAd: AdserverAd | undefined = adserverAds.find(
        ({ position, breakId: daiAdBreakId }) =>
          position === linearAdPosition && breakId === daiAdBreakId
      );

      let matchingAdVast4: AdVast4EmbeddedAdMetadata[] = findAppropriateAd(
        adServerAd?.ads,
        linearAdDuration
      );

      // handle freeWheel fillers
      let freeWheelFillersAds: AdVast4EmbeddedAdMetadata[] | null = [];

      if (
        this.freeWheelFillersAdsProviderService.isPossibleFwFillerSlot(
          !!matchingAdVast4?.length,
          adServerAd?.connector,
          adServerAd && adServerAd.ads.length > 0
        )
      ) {
        const { freewheelAds, fillersAdsTemplates } =
          await this.freeWheelFillersAdsProviderService.getSufficientDurationAds(
            adServerAd as AdserverAdFreeWheel,
            linearAdDuration,
            channel as availableItalyChannels,
            breakId,
            linearAdPosition,
            version,
            requestMacroParams.requestProtocol,
            adId,
            custParams,
            requestMacroParams.uid
          );

        if (freewheelAds) {
          matchingAdVast4 = freewheelAds;
        }

        if (fillersAdsTemplates.length) {
          freeWheelFillersAds = fillersAdsTemplates;
        }
      }

      // provide mirrored ad as fallback for not successfully replaced dai slot
      if (!matchingAdVast4?.length) {
        const mirroredAd = getMirroredAd(breakId, linearAd, adTimeOffset, isDebugMode);

        if (mirroredAd) {
          playlistJson.push(mirroredAd);
        }

        continue;
      }

      // handle successfully replaced slot
      isWithReplacedAds = true;
      const fillersCount = freeWheelFillersAds.length;
      const isWithFillersAds = fillersCount > 0;
      const isWithMultipleAds = isWithFillersAds || matchingAdVast4.length > 1;

      // update adserver ad
      const updatedAdServerAds: AdVast4EmbeddedAdMetadata[] = matchingAdVast4.map(
        (matchingAd) =>
          this.adSlotPlaylistTransformer.updateAdServerAd(
            matchingAd,
            linearAdPosition,
            output === PlaylistOutputs.vast4,
            breakId,
            timeOffsetWithFallback,
            isAdOceanConnector,
            isGamConnector,
            !!breakMetadata?.diffCreatives
          )
      );

      for (const [adIndex, adVast] of updatedAdServerAds.entries()) {
        // update tracking scripts
        playlistJson.push(
          breakMetadata?.trackingDaiAds
            ? new AdTrackingsUpdater(
                adVast,
                adIndex,
                linearAd,
                channel,
                version,
                requestMacroParams?.requestProtocol!,
                requestMacroParams?.uid || '',
                isAdOceanConnector,
                custParams,
                isWithMultipleAds
              ).getUpdatedAd()
            : adVast
        );
      }

      // fill up playlist with freewheel fillers
      if (isWithFillersAds) {
        playlistJson.push(...freeWheelFillersAds);
      }
    }

    const adVasts = this.generalPlaylistTransformer.updateBreakType(
      isWithReplacedAds,
      playlistJson
    );

    const result: IVastJsonOperationalData = {
      vast4Json: prepareJsonVast4WithMetadata(adVasts),
      isWithReplacedAds,
      adServerResponseLog: adserverAds
    };

    logger('CREATE_JSON_PLAYLIST_DATA', { result }, LogLevel.external); // temp external group

    return result;
  }
}
