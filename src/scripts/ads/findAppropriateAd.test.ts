import { findAppropriateAd } from './findAppropriateAd';
import { multipleAdsVast, multipleAdsVastMetadata } from '../../assets/mocks/multipleAdsVast';
import { dur15AdWithMetadata } from '../../assets/mocks/dur15AdVast';
import { dur30AdWithMetadata } from '../../assets/mocks/dur30AdVast';
import { oneAdVast, oneAdVastMetadata } from '../../assets/mocks/oneAdVast';
import { AdVast4EmbeddedAdMetadata } from 'adpod-tools';

describe('findAppropriateAd script test suite', () => {
  const oneAd: AdVast4EmbeddedAdMetadata[] = [
    { adVast: oneAdVast.VAST.Ad[0], adMetadata: oneAdVastMetadata }
  ];
  const multiAd: AdVast4EmbeddedAdMetadata[] = [
    { adVast: multipleAdsVast.VAST.Ad[0], adMetadata: multipleAdsVastMetadata[0] },
    { adVast: multipleAdsVast.VAST.Ad[1], adMetadata: multipleAdsVastMetadata[1] }
  ];

  test('findAppropriateAd is function', () => {
    expect(typeof findAppropriateAd).toEqual('function');
  });

  test('matching ad from list, first in array', () => {
    const matchingAd = findAppropriateAd(multiAd, 15);
    expect(matchingAd[0]).toEqual(dur15AdWithMetadata);
  });

  test('matching ad from list, second in array', () => {
    const matchingAd = findAppropriateAd(multiAd, 30);
    expect(matchingAd[0]).toEqual(dur30AdWithMetadata);
  });

  test('matching ad from list; not found matching by duration 1', () => {
    const matchingAd = findAppropriateAd(multiAd, 10);
    expect(matchingAd).toEqual([]);
  });

  test('matching ad from list; not found matching by duration 2', () => {
    const matchingAd = findAppropriateAd(multiAd, 151);
    expect(matchingAd).toEqual([]);
  });

  test('matching ad from object, first in array', () => {
    const matchingAd = findAppropriateAd(oneAd, 15);
    expect(matchingAd[0]).toEqual(dur15AdWithMetadata);
  });

  test('matching ad from object; not found matching by duration', () => {
    const matchingAd = findAppropriateAd(oneAd, 10);
    expect(matchingAd).toEqual([]);
  });
});
