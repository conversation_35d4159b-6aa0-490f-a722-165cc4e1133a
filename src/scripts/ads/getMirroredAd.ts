import { AdVast4EmbeddedAdMetadata, IAd } from 'adpod-tools';

export const getMirroredAd = (
  bid: string,
  adData: IAd,
  time?: string,
  debugMode?: boolean
): AdVast4EmbeddedAdMetadata | null => {
  const [matchingAd] = adData.vastmirroredadsJson?.VAST.Ad ?? [];
  const [adMetadata] = adData.vastmirroredadsMetadata ?? [];

  if (!matchingAd || !adMetadata) {
    return null;
  }

  adMetadata.breakId = adData.metadata?.breakId || bid;
  adMetadata.linear = 'true';
  matchingAd._attributes.sequence = adData.position;

  if (time) {
    adMetadata.timeOffset = time;
  }

  // add <Debug> tag to mirrored VAST. It contains ad server URL request.
  // it's required to display ad server URL (also) once ad server returns empty vast or HTTP err
  if (debugMode && matchingAd?.InLine) {
    matchingAd.InLine.Debug = {
      AdRequestConnector: {
        _text: adData.connector || ''
      },
      Metadata: {
        _text: JSON.stringify(adData.metadata)
      }
    };
  }

  return {
    adVast: matchingAd,
    adMetadata: {
      ...adMetadata,
      spotId: matchingAd._attributes.id
    }
  };
};
