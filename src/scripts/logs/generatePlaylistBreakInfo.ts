import {
  returnAsArrayEmpty,
  LogLevel,
  AdVast4EmbeddedAdMetadata,
  Vast4EmbeddedAdMetadata
} from 'adpod-tools';
import { AdserverAd, AdserverAdFreeWheel, IPlaylistInfoItem } from '../../interfaces';
import logger from '../../libs/logging/logger';

const isAdserverAdFW = (ad: AdserverAd | undefined): ad is AdserverAdFreeWheel => {
  return !!ad && 'breakFWTrackingScripts' in ad;
};

export const generatePlaylistBreakInfo = (
  vast4Json: Vast4EmbeddedAdMetadata,
  version: string,
  channel: string,
  adServerResponseLog?: AdserverAd[]
): IPlaylistInfoItem | null => {
  if (vast4Json.VAST.Ad) {
    const allPlaylistAds: AdVast4EmbeddedAdMetadata[] = returnAsArrayEmpty(vast4Json.VAST.Ad);

    const bid = allPlaylistAds[0].adMetadata.breakId ?? 'N/A';

    const breakDaiPlaylistAdsCount = allPlaylistAds.filter(
      ({ adMetadata }) => adMetadata.linear !== 'true'
    ).length;

    const breakAllAdsCount = allPlaylistAds.length;

    const connector = adServerResponseLog?.find((a) => !!a.connector)?.connector || 'NA';

    logger(
      `STATS_BREAK_REPLACED_ADS_${breakDaiPlaylistAdsCount}_ALL_ADS_${breakAllAdsCount}_V_${version}_CONNECTOR_${connector}_CHANNEL_${channel}`,
      { breakDaiPlaylistAdsCount, breakAllAdsCount, connector, version, channel },
      LogLevel.statsBreak
    );

    return {
      bid,
      breakDaiPlaylistAdsCount,
      isWithReplacedAds: breakDaiPlaylistAdsCount > 0,
      breakAllAdsCount: allPlaylistAds.length,
      connector: adServerResponseLog ? adServerResponseLog[0]?.connector : 'N/A',
      breakAds: allPlaylistAds.map(({ adVast, adMetadata }) => {
        const { sequence, id } = adVast._attributes;
        const { linear, campaignId, timeOffset } = adMetadata;

        const universalAdId =
          adVast.InLine?.Creatives.Creative[0].UniversalAdId?._text ?? 'N/A';

        const adServerResponseElement = adServerResponseLog?.find(
          (a) => a.breakId === bid && a.position === sequence
        );

        return {
          p: sequence,
          id,
          universalAdId,
          campaignId,
          timeOffset,
          t: linear ? 'TV' : 'DAI',
          adServer: {
            url: adServerResponseElement?.adServerUrl ?? 'N/A',
            fw_slotImpressionEvent: isAdserverAdFW(adServerResponseElement)
              ? !!adServerResponseElement.breakFWTrackingScripts?.slotImpressionUrl
              : false,
            fw_slotEndEvent: isAdserverAdFW(adServerResponseElement)
              ? !!adServerResponseElement?.breakFWTrackingScripts?.slotEndUrl
              : false
          }
        };
      })
    };
  }
  return null;
};
